import type { ExportTaskReq, ExportTaskReqExportBizTypeEnum } from '@/apiv2/file'
import type { PageListInstance } from '@/components/CgPageList'
import { exportTaskApi } from '@/api.services/file.service'
import router from '@/router'

/**
 * 导出配置接口
 */
export interface ExportConfig<T = any, P = any> {
  /** 导出业务类型 */
  exportBizType: ExportTaskReqExportBizTypeEnum
  /** 页面列表引用 */
  pageListRef: Ref<PageListInstance | null>
  /** 查询参数 */
  queryModel: Ref<T>
  /** 参数转换函数 - 将查询参数转换为导出参数 */
  transformParams: (queryParams: T) => P
  /** 获取选中项ID的函数 */
  getSelectedIds: (records: any[]) => number[]
  /** 导出请求参数构建函数 */
  buildExportRequest: (params: { selectedIds?: number[], exportParam?: P }) => any
}

/**
 * 通用导出 Composable
 */
export function useExport<T = any, P = any>(config: ExportConfig<T, P>) {
  const {
    exportBizType,
    pageListRef,
    queryModel,
    transformParams,
    getSelectedIds,
    buildExportRequest,
  } = config

  /**
   * 清理空值参数
   */
  function cleanEmptyParams(params: Record<string, any>): Record<string, any> {
    const cleaned = { ...params }
    Object.keys(cleaned).forEach((key) => {
      const value = cleaned[key]
      if (
        value === undefined
        || value === null
        || String(value) === ''
        || (Array.isArray(value) && value.length === 0)
      ) {
        delete cleaned[key]
      }
    })
    return cleaned
  }

  /**
   * 执行导出
   */
  async function handleExport(_params?: any): Promise<boolean> {
    try {
      // 获取选中的记录
      const selectedRecords = pageListRef.value?.getCheckboxRecords() || []

      let exportRequest: any

      // 如果有选中的记录，则使用选中的ID进行导出
      if (selectedRecords.length > 0) {
        const selectedIds = getSelectedIds(selectedRecords)

        if (selectedIds.length === 0) {
          ElMessage.warning('选中的记录中没有有效的ID，无法导出')
          return false
        }

        exportRequest = buildExportRequest({ selectedIds })
      }
      // 否则使用当前的查询条件进行导出
      else {
        const currentParams = queryModel.value
        const exportParam = transformParams(currentParams)
        const cleanedParam = cleanEmptyParams(exportParam as Record<string, any>)

        exportRequest = buildExportRequest({ exportParam: cleanedParam as P })
      }

      // 调用导出任务API
      const exportTaskReq: ExportTaskReq = {
        exportBizType,
        exportParams: JSON.stringify(exportRequest),
      }

      const result = await exportTaskApi.create1({ exportTaskReq })

      if (result.data.success) {
        ElMessageBox.confirm('导出任务已创建成功，是否前往下载中心查看？', '导出成功', {
          confirmButtonText: '前往下载中心',
          cancelButtonText: '留在当前页面',
          type: 'success',
        })
          .then(() => {
            router.push('/download-center')
          })
          .catch(() => {
            // 用户选择留在当前页面，不做任何操作
          })
        return true
      }
      else {
        ElMessage.error(result.data.message || '导出任务创建失败')
        return false
      }
    }
    catch (error) {
      console.error('导出时发生错误:', error)
      ElMessage.error('导出失败，请稍后重试')
      return false
    }
  }

  return {
    handleExport,
  }
}

/**
 * 产品导出参数转换器
 */
export function createProductExportTransformer() {
  return function transformProductParams(queryParams: any) {
    return {
      categoryIds: queryParams.categoryIds?.length > 0 ? queryParams.categoryIds : undefined,
      supplierIds: queryParams.supplierIds?.length > 0 ? queryParams.supplierIds : undefined,
      propertyValueIds: queryParams.propertyIds?.length > 0 ? queryParams.propertyIds : undefined,
      createTimeStart: queryParams.createTimeStart || undefined,
      createTimeEnd: queryParams.createTimeEnd || undefined,
      maintainerIds: queryParams.maintainerIds?.length > 0 ? queryParams.maintainerIds : undefined,
      assemblyIds: queryParams.assemblyIds?.length > 0 ? queryParams.assemblyIds : undefined,
      status: queryParams.status,
      electronicState: queryParams.electronicState,
      productName: queryParams.productName || undefined,
      productCodes: queryParams.productCodes?.length > 0 ? queryParams.productCodes : undefined,
      productCode: queryParams.productCode || undefined,
      skuCodes: queryParams.skuCodes?.length > 0 ? queryParams.skuCodes : undefined,
      skuCode: queryParams.skuCode || undefined,
      materialIds: queryParams.materialIds?.length > 0 ? queryParams.materialIds : undefined,
    }
  }
}

/**
 * 商品导出参数转换器
 */
export function createCommodityExportTransformer() {
  return function transformCommodityParams(queryParams: any) {
    return {
      locale: queryParams.locale || undefined,
      categoryIds: queryParams.categoryIds?.length > 0
        ? queryParams.categoryIds.filter((id: any): id is number => id !== null)
        : undefined,
      productCodes: queryParams.productCodes?.length > 0 ? queryParams.productCodes : undefined,
      productCode: queryParams.productCode || undefined,
      commodityName: queryParams.commodityName || undefined,
      productStatus: queryParams.productStatus,
      selectionStatus: queryParams.selectionStatus,
      createTimeStart: queryParams.createTimeStart || undefined,
      createTimeEnd: queryParams.createTimeEnd || undefined,
      styles: queryParams.styles?.length > 0 ? queryParams.styles : undefined,
      platformIds: queryParams.platformIds?.length > 0 ? queryParams.platformIds : undefined,
      listingStatuses: queryParams.listingStatuses?.length > 0 ? queryParams.listingStatuses : undefined,
    }
  }
}
