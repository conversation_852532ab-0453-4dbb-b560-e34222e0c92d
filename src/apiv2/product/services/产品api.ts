/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ProductBatchPropertiesUpdateRequest } from '../models';
// @ts-ignore
import type { ProductBatchStatusUpdateRequest } from '../models';
// @ts-ignore
import type { ProductCreateRequest } from '../models';
// @ts-ignore
import type { ProductImportReq } from '../models';
// @ts-ignore
import type { ProductInitTicketCreateReqVO } from '../models';
// @ts-ignore
import type { ProductSearchParam } from '../models';
// @ts-ignore
import type { ProductUpdateRequest } from '../models';
// @ts-ignore
import type { ResultVOBoolean } from '../models';
// @ts-ignore
import type { ResultVOInteger } from '../models';
// @ts-ignore
import type { ResultVOListCountryVO } from '../models';
// @ts-ignore
import type { ResultVOListProductDetailVO } from '../models';
// @ts-ignore
import type { ResultVOListProductSkuPriceVO } from '../models';
// @ts-ignore
import type { ResultVOPageVOInteger } from '../models';
// @ts-ignore
import type { ResultVOPageVOProductListVO } from '../models';
// @ts-ignore
import type { ResultVOProductDetailVO } from '../models';
// @ts-ignore
import type { ResultVOProductInitTicketCreateResultDetailVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
/**
 * 产品Api - axios parameter creator
 * @export
 */
export const 产品ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 批量创建产品开发工单
         * @param {ProductInitTicketCreateReqVO} productInitTicketCreateReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchCreateTickets: async (productInitTicketCreateReqVO: ProductInitTicketCreateReqVO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'productInitTicketCreateReqVO' is not null or undefined
            assertParamExists('batchCreateTickets', 'productInitTicketCreateReqVO', productInitTicketCreateReqVO)
            const localVarPath = `/api/products/create-ticket`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(productInitTicketCreateReqVO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取sku物流信息对应的不同国家的产品定价
         * @summary 批量获取sku的所有产品定价
         * @param {Array<number>} requestBody 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchListProductSkuPrices: async (requestBody: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'requestBody' is not null or undefined
            assertParamExists('batchListProductSkuPrices', 'requestBody', requestBody)
            const localVarPath = `/api/products/batchPrices`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 支持批量修改指定产品的标签
         * @summary 批量修改产品标签
         * @param {ProductBatchPropertiesUpdateRequest} productBatchPropertiesUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchUpdateProperties: async (productBatchPropertiesUpdateRequest: ProductBatchPropertiesUpdateRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'productBatchPropertiesUpdateRequest' is not null or undefined
            assertParamExists('batchUpdateProperties', 'productBatchPropertiesUpdateRequest', productBatchPropertiesUpdateRequest)
            const localVarPath = `/api/products/properties`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(productBatchPropertiesUpdateRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 支持批量修改指定产品的状态
         * @summary 批量修改产品状态
         * @param {ProductBatchStatusUpdateRequest} productBatchStatusUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchUpdateStatus: async (productBatchStatusUpdateRequest: ProductBatchStatusUpdateRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'productBatchStatusUpdateRequest' is not null or undefined
            assertParamExists('batchUpdateStatus', 'productBatchStatusUpdateRequest', productBatchStatusUpdateRequest)
            const localVarPath = `/api/products/status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(productBatchStatusUpdateRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 创建产品
         * @param {ProductCreateRequest} productCreateRequest 
         * @param {number} [priority] 工单优先级，值越大优先级越高
         * @param {boolean} [ticketCreated] 是否创建工单
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createProduct: async (productCreateRequest: ProductCreateRequest, priority?: number, ticketCreated?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'productCreateRequest' is not null or undefined
            assertParamExists('createProduct', 'productCreateRequest', productCreateRequest)
            const localVarPath = `/api/products`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (priority !== undefined) {
                localVarQueryParameter['priority'] = priority;
            }

            if (ticketCreated !== undefined) {
                localVarQueryParameter['ticketCreated'] = ticketCreated;
            }


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(productCreateRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 下载元字段导入模板
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        downloadMetaFieldTemplate: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/products/template/meta-field`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 下载导入模板
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        downloadTemplate: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/products/template`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取产品详情
         * @param {number} productId 产品ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProduct: async (productId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'productId' is not null or undefined
            assertParamExists('getProduct', 'productId', productId)
            const localVarPath = `/api/products/{productId}`
                .replace(`{${"productId"}}`, encodeURIComponent(String(productId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导入产品
         * @param {ProductImportReq} productImportReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importProducts: async (productImportReq: ProductImportReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'productImportReq' is not null or undefined
            assertParamExists('importProducts', 'productImportReq', productImportReq)
            const localVarPath = `/api/products/template`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(productImportReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取系统支持的所有国家编码和名称
         * @summary 获取国家列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listCountries: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/products/countries`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页搜索获取产品id
         * @param {ProductSearchParam} productSearchParam 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listProductId: async (productSearchParam: ProductSearchParam, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'productSearchParam' is not null or undefined
            assertParamExists('listProductId', 'productSearchParam', productSearchParam)
            const localVarPath = `/api/products/listProductId`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(productSearchParam, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取sku物流信息对应的不同国家的产品定价
         * @summary 获取sku的所有产品定价
         * @param {number} skuId skuId
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listProductSkuPrices: async (skuId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'skuId' is not null or undefined
            assertParamExists('listProductSkuPrices', 'skuId', skuId)
            const localVarPath = `/api/products/prices`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (skuId !== undefined) {
                localVarQueryParameter['skuId'] = skuId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 搜索产品列表
         * @param {ProductSearchParam} productSearchParam 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listProducts: async (productSearchParam: ProductSearchParam, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'productSearchParam' is not null or undefined
            assertParamExists('listProducts', 'productSearchParam', productSearchParam)
            const localVarPath = `/api/products/_search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(productSearchParam, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量查询产品详情
         * @param {Array<number>} requestBody 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryProductDetail: async (requestBody: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'requestBody' is not null or undefined
            assertParamExists('queryProductDetail', 'requestBody', requestBody)
            const localVarPath = `/api/products/queryProductDetail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 全量同步产品到搜索引擎
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        syncProducts: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/products/_sync`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 更新产品及其所有子对象（SKU、图片、文件等）
         * @summary 全量更新产品
         * @param {ProductUpdateRequest} productUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateProduct: async (productUpdateRequest: ProductUpdateRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'productUpdateRequest' is not null or undefined
            assertParamExists('updateProduct', 'productUpdateRequest', productUpdateRequest)
            const localVarPath = `/api/products`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(productUpdateRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 产品Api - functional programming interface
 * @export
 */
export const 产品ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 产品ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 批量创建产品开发工单
         * @param {ProductInitTicketCreateReqVO} productInitTicketCreateReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async batchCreateTickets(productInitTicketCreateReqVO: ProductInitTicketCreateReqVO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOProductInitTicketCreateResultDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.batchCreateTickets(productInitTicketCreateReqVO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.batchCreateTickets']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 获取sku物流信息对应的不同国家的产品定价
         * @summary 批量获取sku的所有产品定价
         * @param {Array<number>} requestBody 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async batchListProductSkuPrices(requestBody: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListProductSkuPriceVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.batchListProductSkuPrices(requestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.batchListProductSkuPrices']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 支持批量修改指定产品的标签
         * @summary 批量修改产品标签
         * @param {ProductBatchPropertiesUpdateRequest} productBatchPropertiesUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async batchUpdateProperties(productBatchPropertiesUpdateRequest: ProductBatchPropertiesUpdateRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOBoolean>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.batchUpdateProperties(productBatchPropertiesUpdateRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.batchUpdateProperties']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 支持批量修改指定产品的状态
         * @summary 批量修改产品状态
         * @param {ProductBatchStatusUpdateRequest} productBatchStatusUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async batchUpdateStatus(productBatchStatusUpdateRequest: ProductBatchStatusUpdateRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOBoolean>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.batchUpdateStatus(productBatchStatusUpdateRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.batchUpdateStatus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 创建产品
         * @param {ProductCreateRequest} productCreateRequest 
         * @param {number} [priority] 工单优先级，值越大优先级越高
         * @param {boolean} [ticketCreated] 是否创建工单
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createProduct(productCreateRequest: ProductCreateRequest, priority?: number, ticketCreated?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOInteger>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createProduct(productCreateRequest, priority, ticketCreated, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.createProduct']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 下载元字段导入模板
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async downloadMetaFieldTemplate(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.downloadMetaFieldTemplate(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.downloadMetaFieldTemplate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 下载导入模板
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async downloadTemplate(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.downloadTemplate(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.downloadTemplate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 获取产品详情
         * @param {number} productId 产品ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProduct(productId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOProductDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProduct(productId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.getProduct']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 导入产品
         * @param {ProductImportReq} productImportReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async importProducts(productImportReq: ProductImportReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.importProducts(productImportReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.importProducts']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 获取系统支持的所有国家编码和名称
         * @summary 获取国家列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listCountries(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListCountryVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listCountries(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.listCountries']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 分页搜索获取产品id
         * @param {ProductSearchParam} productSearchParam 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listProductId(productSearchParam: ProductSearchParam, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOInteger>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listProductId(productSearchParam, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.listProductId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 获取sku物流信息对应的不同国家的产品定价
         * @summary 获取sku的所有产品定价
         * @param {number} skuId skuId
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listProductSkuPrices(skuId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListProductSkuPriceVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listProductSkuPrices(skuId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.listProductSkuPrices']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 搜索产品列表
         * @param {ProductSearchParam} productSearchParam 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listProducts(productSearchParam: ProductSearchParam, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOProductListVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listProducts(productSearchParam, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.listProducts']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 批量查询产品详情
         * @param {Array<number>} requestBody 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async queryProductDetail(requestBody: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListProductDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.queryProductDetail(requestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.queryProductDetail']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 全量同步产品到搜索引擎
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async syncProducts(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.syncProducts(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.syncProducts']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 更新产品及其所有子对象（SKU、图片、文件等）
         * @summary 全量更新产品
         * @param {ProductUpdateRequest} productUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateProduct(productUpdateRequest: ProductUpdateRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOBoolean>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateProduct(productUpdateRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品Api.updateProduct']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 产品Api - factory interface
 * @export
 */
export const 产品ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 产品ApiFp(configuration)
    return {
        /**
         * 
         * @summary 批量创建产品开发工单
         * @param {产品ApiBatchCreateTicketsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchCreateTickets(requestParameters: 产品ApiBatchCreateTicketsRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOProductInitTicketCreateResultDetailVO> {
            return localVarFp.batchCreateTickets(requestParameters.productInitTicketCreateReqVO, options).then((request) => request(axios, basePath));
        },
        /**
         * 获取sku物流信息对应的不同国家的产品定价
         * @summary 批量获取sku的所有产品定价
         * @param {产品ApiBatchListProductSkuPricesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchListProductSkuPrices(requestParameters: 产品ApiBatchListProductSkuPricesRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListProductSkuPriceVO> {
            return localVarFp.batchListProductSkuPrices(requestParameters.requestBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 支持批量修改指定产品的标签
         * @summary 批量修改产品标签
         * @param {产品ApiBatchUpdatePropertiesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchUpdateProperties(requestParameters: 产品ApiBatchUpdatePropertiesRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOBoolean> {
            return localVarFp.batchUpdateProperties(requestParameters.productBatchPropertiesUpdateRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 支持批量修改指定产品的状态
         * @summary 批量修改产品状态
         * @param {产品ApiBatchUpdateStatusRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchUpdateStatus(requestParameters: 产品ApiBatchUpdateStatusRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOBoolean> {
            return localVarFp.batchUpdateStatus(requestParameters.productBatchStatusUpdateRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 创建产品
         * @param {产品ApiCreateProductRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createProduct(requestParameters: 产品ApiCreateProductRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOInteger> {
            return localVarFp.createProduct(requestParameters.productCreateRequest, requestParameters.priority, requestParameters.ticketCreated, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 下载元字段导入模板
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        downloadMetaFieldTemplate(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.downloadMetaFieldTemplate(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 下载导入模板
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        downloadTemplate(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.downloadTemplate(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取产品详情
         * @param {产品ApiGetProductRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProduct(requestParameters: 产品ApiGetProductRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOProductDetailVO> {
            return localVarFp.getProduct(requestParameters.productId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导入产品
         * @param {产品ApiImportProductsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importProducts(requestParameters: 产品ApiImportProductsRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.importProducts(requestParameters.productImportReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 获取系统支持的所有国家编码和名称
         * @summary 获取国家列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listCountries(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListCountryVO> {
            return localVarFp.listCountries(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页搜索获取产品id
         * @param {产品ApiListProductIdRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listProductId(requestParameters: 产品ApiListProductIdRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOInteger> {
            return localVarFp.listProductId(requestParameters.productSearchParam, options).then((request) => request(axios, basePath));
        },
        /**
         * 获取sku物流信息对应的不同国家的产品定价
         * @summary 获取sku的所有产品定价
         * @param {产品ApiListProductSkuPricesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listProductSkuPrices(requestParameters: 产品ApiListProductSkuPricesRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListProductSkuPriceVO> {
            return localVarFp.listProductSkuPrices(requestParameters.skuId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 搜索产品列表
         * @param {产品ApiListProductsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listProducts(requestParameters: 产品ApiListProductsRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOProductListVO> {
            return localVarFp.listProducts(requestParameters.productSearchParam, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量查询产品详情
         * @param {产品ApiQueryProductDetailRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryProductDetail(requestParameters: 产品ApiQueryProductDetailRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListProductDetailVO> {
            return localVarFp.queryProductDetail(requestParameters.requestBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 全量同步产品到搜索引擎
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        syncProducts(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.syncProducts(options).then((request) => request(axios, basePath));
        },
        /**
         * 更新产品及其所有子对象（SKU、图片、文件等）
         * @summary 全量更新产品
         * @param {产品ApiUpdateProductRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateProduct(requestParameters: 产品ApiUpdateProductRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOBoolean> {
            return localVarFp.updateProduct(requestParameters.productUpdateRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for batchCreateTickets operation in 产品Api.
 * @export
 * @interface 产品ApiBatchCreateTicketsRequest
 */
export interface 产品ApiBatchCreateTicketsRequest {
    /**
     * 
     * @type {ProductInitTicketCreateReqVO}
     * @memberof 产品ApiBatchCreateTickets
     */
    readonly productInitTicketCreateReqVO: ProductInitTicketCreateReqVO
}

/**
 * Request parameters for batchListProductSkuPrices operation in 产品Api.
 * @export
 * @interface 产品ApiBatchListProductSkuPricesRequest
 */
export interface 产品ApiBatchListProductSkuPricesRequest {
    /**
     * 
     * @type {Array<number>}
     * @memberof 产品ApiBatchListProductSkuPrices
     */
    readonly requestBody: Array<number>
}

/**
 * Request parameters for batchUpdateProperties operation in 产品Api.
 * @export
 * @interface 产品ApiBatchUpdatePropertiesRequest
 */
export interface 产品ApiBatchUpdatePropertiesRequest {
    /**
     * 
     * @type {ProductBatchPropertiesUpdateRequest}
     * @memberof 产品ApiBatchUpdateProperties
     */
    readonly productBatchPropertiesUpdateRequest: ProductBatchPropertiesUpdateRequest
}

/**
 * Request parameters for batchUpdateStatus operation in 产品Api.
 * @export
 * @interface 产品ApiBatchUpdateStatusRequest
 */
export interface 产品ApiBatchUpdateStatusRequest {
    /**
     * 
     * @type {ProductBatchStatusUpdateRequest}
     * @memberof 产品ApiBatchUpdateStatus
     */
    readonly productBatchStatusUpdateRequest: ProductBatchStatusUpdateRequest
}

/**
 * Request parameters for createProduct operation in 产品Api.
 * @export
 * @interface 产品ApiCreateProductRequest
 */
export interface 产品ApiCreateProductRequest {
    /**
     * 
     * @type {ProductCreateRequest}
     * @memberof 产品ApiCreateProduct
     */
    readonly productCreateRequest: ProductCreateRequest

    /**
     * 工单优先级，值越大优先级越高
     * @type {number}
     * @memberof 产品ApiCreateProduct
     */
    readonly priority?: number

    /**
     * 是否创建工单
     * @type {boolean}
     * @memberof 产品ApiCreateProduct
     */
    readonly ticketCreated?: boolean
}

/**
 * Request parameters for getProduct operation in 产品Api.
 * @export
 * @interface 产品ApiGetProductRequest
 */
export interface 产品ApiGetProductRequest {
    /**
     * 产品ID
     * @type {number}
     * @memberof 产品ApiGetProduct
     */
    readonly productId: number
}

/**
 * Request parameters for importProducts operation in 产品Api.
 * @export
 * @interface 产品ApiImportProductsRequest
 */
export interface 产品ApiImportProductsRequest {
    /**
     * 
     * @type {ProductImportReq}
     * @memberof 产品ApiImportProducts
     */
    readonly productImportReq: ProductImportReq
}

/**
 * Request parameters for listProductId operation in 产品Api.
 * @export
 * @interface 产品ApiListProductIdRequest
 */
export interface 产品ApiListProductIdRequest {
    /**
     * 
     * @type {ProductSearchParam}
     * @memberof 产品ApiListProductId
     */
    readonly productSearchParam: ProductSearchParam
}

/**
 * Request parameters for listProductSkuPrices operation in 产品Api.
 * @export
 * @interface 产品ApiListProductSkuPricesRequest
 */
export interface 产品ApiListProductSkuPricesRequest {
    /**
     * skuId
     * @type {number}
     * @memberof 产品ApiListProductSkuPrices
     */
    readonly skuId: number
}

/**
 * Request parameters for listProducts operation in 产品Api.
 * @export
 * @interface 产品ApiListProductsRequest
 */
export interface 产品ApiListProductsRequest {
    /**
     * 
     * @type {ProductSearchParam}
     * @memberof 产品ApiListProducts
     */
    readonly productSearchParam: ProductSearchParam
}

/**
 * Request parameters for queryProductDetail operation in 产品Api.
 * @export
 * @interface 产品ApiQueryProductDetailRequest
 */
export interface 产品ApiQueryProductDetailRequest {
    /**
     * 
     * @type {Array<number>}
     * @memberof 产品ApiQueryProductDetail
     */
    readonly requestBody: Array<number>
}

/**
 * Request parameters for updateProduct operation in 产品Api.
 * @export
 * @interface 产品ApiUpdateProductRequest
 */
export interface 产品ApiUpdateProductRequest {
    /**
     * 
     * @type {ProductUpdateRequest}
     * @memberof 产品ApiUpdateProduct
     */
    readonly productUpdateRequest: ProductUpdateRequest
}

/**
 * 产品Api - object-oriented interface
 * @export
 * @class 产品Api
 * @extends {BaseAPI}
 */
export class 产品Api extends BaseAPI {
    /**
     * 
     * @summary 批量创建产品开发工单
     * @param {产品ApiBatchCreateTicketsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public batchCreateTickets(requestParameters: 产品ApiBatchCreateTicketsRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).batchCreateTickets(requestParameters.productInitTicketCreateReqVO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 获取sku物流信息对应的不同国家的产品定价
     * @summary 批量获取sku的所有产品定价
     * @param {产品ApiBatchListProductSkuPricesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public batchListProductSkuPrices(requestParameters: 产品ApiBatchListProductSkuPricesRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).batchListProductSkuPrices(requestParameters.requestBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 支持批量修改指定产品的标签
     * @summary 批量修改产品标签
     * @param {产品ApiBatchUpdatePropertiesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public batchUpdateProperties(requestParameters: 产品ApiBatchUpdatePropertiesRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).batchUpdateProperties(requestParameters.productBatchPropertiesUpdateRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 支持批量修改指定产品的状态
     * @summary 批量修改产品状态
     * @param {产品ApiBatchUpdateStatusRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public batchUpdateStatus(requestParameters: 产品ApiBatchUpdateStatusRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).batchUpdateStatus(requestParameters.productBatchStatusUpdateRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 创建产品
     * @param {产品ApiCreateProductRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public createProduct(requestParameters: 产品ApiCreateProductRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).createProduct(requestParameters.productCreateRequest, requestParameters.priority, requestParameters.ticketCreated, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 下载元字段导入模板
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public downloadMetaFieldTemplate(options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).downloadMetaFieldTemplate(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 下载导入模板
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public downloadTemplate(options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).downloadTemplate(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 获取产品详情
     * @param {产品ApiGetProductRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public getProduct(requestParameters: 产品ApiGetProductRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).getProduct(requestParameters.productId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 导入产品
     * @param {产品ApiImportProductsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public importProducts(requestParameters: 产品ApiImportProductsRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).importProducts(requestParameters.productImportReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 获取系统支持的所有国家编码和名称
     * @summary 获取国家列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public listCountries(options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).listCountries(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 分页搜索获取产品id
     * @param {产品ApiListProductIdRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public listProductId(requestParameters: 产品ApiListProductIdRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).listProductId(requestParameters.productSearchParam, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 获取sku物流信息对应的不同国家的产品定价
     * @summary 获取sku的所有产品定价
     * @param {产品ApiListProductSkuPricesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public listProductSkuPrices(requestParameters: 产品ApiListProductSkuPricesRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).listProductSkuPrices(requestParameters.skuId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 搜索产品列表
     * @param {产品ApiListProductsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public listProducts(requestParameters: 产品ApiListProductsRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).listProducts(requestParameters.productSearchParam, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 批量查询产品详情
     * @param {产品ApiQueryProductDetailRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public queryProductDetail(requestParameters: 产品ApiQueryProductDetailRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).queryProductDetail(requestParameters.requestBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 全量同步产品到搜索引擎
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public syncProducts(options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).syncProducts(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 更新产品及其所有子对象（SKU、图片、文件等）
     * @summary 全量更新产品
     * @param {产品ApiUpdateProductRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品Api
     */
    public updateProduct(requestParameters: 产品ApiUpdateProductRequest, options?: RawAxiosRequestConfig) {
        return 产品ApiFp(this.configuration).updateProduct(requestParameters.productUpdateRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

