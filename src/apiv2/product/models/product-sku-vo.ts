/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { DictValueVO } from './dict-value-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuLogisticsVO } from './product-sku-logistics-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuPackageVO } from './product-sku-package-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { PropertyValueVO } from './property-value-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SimpleCategoryVO } from './simple-category-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SkuSpecVO } from './sku-spec-vo';

/**
 * sku信息
 * @export
 * @interface ProductSkuVO
 */
export interface ProductSkuVO {
    /**
     * id
     * @type {number}
     * @memberof ProductSkuVO
     */
    'id'?: number;
    /**
     * 缩略图
     * @type {string}
     * @memberof ProductSkuVO
     */
    'thumbnail'?: string;
    /**
     * sku名称
     * @type {string}
     * @memberof ProductSkuVO
     */
    'skuName'?: string;
    /**
     * sku英文名称
     * @type {string}
     * @memberof ProductSkuVO
     */
    'skuNameEn'?: string;
    /**
     * sku编码
     * @type {string}
     * @memberof ProductSkuVO
     */
    'skuCode'?: string;
    /**
     * sku类目
     * @type {Array<SimpleCategoryVO>}
     * @memberof ProductSkuVO
     */
    'categories'?: Array<SimpleCategoryVO>;
    /**
     * 标签
     * @type {Array<PropertyValueVO>}
     * @memberof ProductSkuVO
     */
    'properties'?: Array<PropertyValueVO>;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductSkuVO
     */
    'color'?: DictValueVO;
    /**
     * sku规格
     * @type {Array<SkuSpecVO>}
     * @memberof ProductSkuVO
     */
    'specs'?: Array<SkuSpecVO>;
    /**
     * sku包装
     * @type {Array<ProductSkuPackageVO>}
     * @memberof ProductSkuVO
     */
    'packages'?: Array<ProductSkuPackageVO>;
    /**
     * sku物流信息
     * @type {Array<ProductSkuLogisticsVO>}
     * @memberof ProductSkuVO
     */
    'logistics'?: Array<ProductSkuLogisticsVO>;
    /**
     * 净重（g）
     * @type {number}
     * @memberof ProductSkuVO
     */
    'netWeight'?: number;
    /**
     * 毛重（g）
     * @type {number}
     * @memberof ProductSkuVO
     */
    'grossWeight'?: number;
    /**
     * 含税含运价格
     * @type {number}
     * @memberof ProductSkuVO
     */
    'purchasePrice'?: number;
    /**
     * 含税不含运价格
     * @type {number}
     * @memberof ProductSkuVO
     */
    'purchasePriceExFreight'?: number;
    /**
     * 税率
     * @type {number}
     * @memberof ProductSkuVO
     */
    'taxRate'?: number;
    /**
     * 平台价格
     * @type {number}
     * @memberof ProductSkuVO
     */
    'platformPrice'?: number;
    /**
     * 采购备注
     * @type {string}
     * @memberof ProductSkuVO
     */
    'purchaseRemark'?: string;
    /**
     * 描述
     * @type {string}
     * @memberof ProductSkuVO
     */
    'description'?: string;
    /**
     * sku 状态
     * @type {number}
     * @memberof ProductSkuVO
     */
    'status'?: ProductSkuVOStatusEnum;
    /**
     * 附带品
     * @type {string}
     * @memberof ProductSkuVO
     */
    'appendant'?: string;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductSkuVO
     */
    'installDifficulty'?: DictValueVO;
    /**
     * 是否带电
     * @type {boolean}
     * @memberof ProductSkuVO
     */
    'electronicState'?: boolean;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductSkuVO
     */
    'electronicCertification'?: DictValueVO;
    /**
     * 电子配件
     * @type {Array<DictValueVO>}
     * @memberof ProductSkuVO
     */
    'electronicAccessory'?: Array<DictValueVO>;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductSkuVO
     */
    'battery'?: DictValueVO;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductSkuVO
     */
    'fragile'?: DictValueVO;
    /**
     * 创建时间
     * @type {string}
     * @memberof ProductSkuVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof ProductSkuVO
     */
    'updateTime'?: string;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductSkuVO
     */
    'priMatLv1'?: DictValueVO;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductSkuVO
     */
    'priMatLv2'?: DictValueVO;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductSkuVO
     */
    'secMatLv1'?: DictValueVO;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductSkuVO
     */
    'secMatLv2'?: DictValueVO;
    /**
     * 材质描述
     * @type {string}
     * @memberof ProductSkuVO
     */
    'materialDesc'?: string;
    /**
     * 规格1
     * @type {string}
     * @memberof ProductSkuVO
     */
    'spec1'?: string;
    /**
     * 规格2
     * @type {string}
     * @memberof ProductSkuVO
     */
    'spec2'?: string;
    /**
     * 规格3
     * @type {string}
     * @memberof ProductSkuVO
     */
    'spec3'?: string;
    /**
     * 规格4
     * @type {string}
     * @memberof ProductSkuVO
     */
    'spec4'?: string;
    /**
     * 规格5
     * @type {string}
     * @memberof ProductSkuVO
     */
    'spec5'?: string;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductSkuVO
     */
    'installDocCollectionState'?: DictValueVO;
    /**
     * 网采链接
     * @type {string}
     * @memberof ProductSkuVO
     */
    'onlinePurchaseLink'?: string;
    /**
     * 供应商发货编码
     * @type {string}
     * @memberof ProductSkuVO
     */
    'supplierDeliveryCode'?: string;
}

export const ProductSkuVOStatusEnum = {
    DRAFT: 0,
    CHECKING: 1,
    ON_SHELF: 2,
    OFF_SHELVES: 3,
    FREEZE: 4,
    DELETED: 5
} as const;

export type ProductSkuVOStatusEnum = typeof ProductSkuVOStatusEnum[keyof typeof ProductSkuVOStatusEnum];


