/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ExportTaskPageReq } from '../models';
// @ts-ignore
import type { ExportTaskReq } from '../models';
// @ts-ignore
import type { ResultVOPageVOExportTaskListVO } from '../models';
// @ts-ignore
import type { ResultVOString } from '../models';
/**
 * 导出任务ApiApi - axios parameter creator
 * @export
 */
export const 导出任务ApiApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 生成导出任务
         * @param {ExportTaskReq} exportTaskReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create1: async (exportTaskReq: ExportTaskReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'exportTaskReq' is not null or undefined
            assertParamExists('create1', 'exportTaskReq', exportTaskReq)
            const localVarPath = `/api/export/create`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(exportTaskReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 生成导出链接
         * @param {number} taskId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        generateExportUrl: async (taskId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'taskId' is not null or undefined
            assertParamExists('generateExportUrl', 'taskId', taskId)
            const localVarPath = `/api/export/{taskId}`
                .replace(`{${"taskId"}}`, encodeURIComponent(String(taskId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 分页查询下载任务列表
         * @summary 分页查询下载任务列表
         * @param {ExportTaskPageReq} exportTaskPageReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listByPage1: async (exportTaskPageReq: ExportTaskPageReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'exportTaskPageReq' is not null or undefined
            assertParamExists('listByPage1', 'exportTaskPageReq', exportTaskPageReq)
            const localVarPath = `/api/export/listByPage`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(exportTaskPageReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 导出任务ApiApi - functional programming interface
 * @export
 */
export const 导出任务ApiApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 导出任务ApiApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 生成导出任务
         * @param {ExportTaskReq} exportTaskReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async create1(exportTaskReq: ExportTaskReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOString>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.create1(exportTaskReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['导出任务ApiApi.create1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 生成导出链接
         * @param {number} taskId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async generateExportUrl(taskId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOString>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.generateExportUrl(taskId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['导出任务ApiApi.generateExportUrl']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 分页查询下载任务列表
         * @summary 分页查询下载任务列表
         * @param {ExportTaskPageReq} exportTaskPageReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listByPage1(exportTaskPageReq: ExportTaskPageReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOExportTaskListVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listByPage1(exportTaskPageReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['导出任务ApiApi.listByPage1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 导出任务ApiApi - factory interface
 * @export
 */
export const 导出任务ApiApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 导出任务ApiApiFp(configuration)
    return {
        /**
         * 
         * @summary 生成导出任务
         * @param {导出任务ApiApiCreate1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create1(requestParameters: 导出任务ApiApiCreate1Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOString> {
            return localVarFp.create1(requestParameters.exportTaskReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 生成导出链接
         * @param {导出任务ApiApiGenerateExportUrlRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        generateExportUrl(requestParameters: 导出任务ApiApiGenerateExportUrlRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOString> {
            return localVarFp.generateExportUrl(requestParameters.taskId, options).then((request) => request(axios, basePath));
        },
        /**
         * 分页查询下载任务列表
         * @summary 分页查询下载任务列表
         * @param {导出任务ApiApiListByPage1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listByPage1(requestParameters: 导出任务ApiApiListByPage1Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOExportTaskListVO> {
            return localVarFp.listByPage1(requestParameters.exportTaskPageReq, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for create1 operation in 导出任务ApiApi.
 * @export
 * @interface 导出任务ApiApiCreate1Request
 */
export interface 导出任务ApiApiCreate1Request {
    /**
     * 
     * @type {ExportTaskReq}
     * @memberof 导出任务ApiApiCreate1
     */
    readonly exportTaskReq: ExportTaskReq
}

/**
 * Request parameters for generateExportUrl operation in 导出任务ApiApi.
 * @export
 * @interface 导出任务ApiApiGenerateExportUrlRequest
 */
export interface 导出任务ApiApiGenerateExportUrlRequest {
    /**
     * 
     * @type {number}
     * @memberof 导出任务ApiApiGenerateExportUrl
     */
    readonly taskId: number
}

/**
 * Request parameters for listByPage1 operation in 导出任务ApiApi.
 * @export
 * @interface 导出任务ApiApiListByPage1Request
 */
export interface 导出任务ApiApiListByPage1Request {
    /**
     * 
     * @type {ExportTaskPageReq}
     * @memberof 导出任务ApiApiListByPage1
     */
    readonly exportTaskPageReq: ExportTaskPageReq
}

/**
 * 导出任务ApiApi - object-oriented interface
 * @export
 * @class 导出任务ApiApi
 * @extends {BaseAPI}
 */
export class 导出任务ApiApi extends BaseAPI {
    /**
     * 
     * @summary 生成导出任务
     * @param {导出任务ApiApiCreate1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 导出任务ApiApi
     */
    public create1(requestParameters: 导出任务ApiApiCreate1Request, options?: RawAxiosRequestConfig) {
        return 导出任务ApiApiFp(this.configuration).create1(requestParameters.exportTaskReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 生成导出链接
     * @param {导出任务ApiApiGenerateExportUrlRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 导出任务ApiApi
     */
    public generateExportUrl(requestParameters: 导出任务ApiApiGenerateExportUrlRequest, options?: RawAxiosRequestConfig) {
        return 导出任务ApiApiFp(this.configuration).generateExportUrl(requestParameters.taskId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 分页查询下载任务列表
     * @summary 分页查询下载任务列表
     * @param {导出任务ApiApiListByPage1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 导出任务ApiApi
     */
    public listByPage1(requestParameters: 导出任务ApiApiListByPage1Request, options?: RawAxiosRequestConfig) {
        return 导出任务ApiApiFp(this.configuration).listByPage1(requestParameters.exportTaskPageReq, options).then((request) => request(this.axios, this.basePath));
    }
}

