/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOTaskCallbackVO } from '../models';
// @ts-ignore
import type { TaskCallbackRequest } from '../models';
/**
 * 任务回调ApiApi - axios parameter creator
 * @export
 */
export const 任务回调ApiApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 上传文件回调
         * @summary 上传文件回调
         * @param {TaskCallbackRequest} taskCallbackRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importsCallback: async (taskCallbackRequest: TaskCallbackRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'taskCallbackRequest' is not null or undefined
            assertParamExists('importsCallback', 'taskCallbackRequest', taskCallbackRequest)
            const localVarPath = `/api/v1/task/callback/imports`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(taskCallbackRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 任务回调ApiApi - functional programming interface
 * @export
 */
export const 任务回调ApiApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 任务回调ApiApiAxiosParamCreator(configuration)
    return {
        /**
         * 上传文件回调
         * @summary 上传文件回调
         * @param {TaskCallbackRequest} taskCallbackRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async importsCallback(taskCallbackRequest: TaskCallbackRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOTaskCallbackVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.importsCallback(taskCallbackRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['任务回调ApiApi.importsCallback']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 任务回调ApiApi - factory interface
 * @export
 */
export const 任务回调ApiApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 任务回调ApiApiFp(configuration)
    return {
        /**
         * 上传文件回调
         * @summary 上传文件回调
         * @param {任务回调ApiApiImportsCallbackRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importsCallback(requestParameters: 任务回调ApiApiImportsCallbackRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOTaskCallbackVO> {
            return localVarFp.importsCallback(requestParameters.taskCallbackRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for importsCallback operation in 任务回调ApiApi.
 * @export
 * @interface 任务回调ApiApiImportsCallbackRequest
 */
export interface 任务回调ApiApiImportsCallbackRequest {
    /**
     * 
     * @type {TaskCallbackRequest}
     * @memberof 任务回调ApiApiImportsCallback
     */
    readonly taskCallbackRequest: TaskCallbackRequest
}

/**
 * 任务回调ApiApi - object-oriented interface
 * @export
 * @class 任务回调ApiApi
 * @extends {BaseAPI}
 */
export class 任务回调ApiApi extends BaseAPI {
    /**
     * 上传文件回调
     * @summary 上传文件回调
     * @param {任务回调ApiApiImportsCallbackRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 任务回调ApiApi
     */
    public importsCallback(requestParameters: 任务回调ApiApiImportsCallbackRequest, options?: RawAxiosRequestConfig) {
        return 任务回调ApiApiFp(this.configuration).importsCallback(requestParameters.taskCallbackRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

