/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ImportsRequest } from '../models';
// @ts-ignore
import type { ImportsTaskReq } from '../models';
// @ts-ignore
import type { ResultVOImportsSubmitVO } from '../models';
// @ts-ignore
import type { ResultVOPageVOImportsTaskVO } from '../models';
/**
 * 导入接口Api - axios parameter creator
 * @export
 */
export const 导入接口ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 导入文件，根据参数区分导入业务
         * @summary 导入文件
         * @param {ImportsRequest} importsRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create: async (importsRequest: ImportsRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'importsRequest' is not null or undefined
            assertParamExists('create', 'importsRequest', importsRequest)
            const localVarPath = `/api/imports/task/create`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(importsRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 分页导入任务列表
         * @summary 分页导入任务列表
         * @param {ImportsTaskReq} importsTaskReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listByPage: async (importsTaskReq: ImportsTaskReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'importsTaskReq' is not null or undefined
            assertParamExists('listByPage', 'importsTaskReq', importsTaskReq)
            const localVarPath = `/api/imports/task/listByPage`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(importsTaskReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 导入接口Api - functional programming interface
 * @export
 */
export const 导入接口ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 导入接口ApiAxiosParamCreator(configuration)
    return {
        /**
         * 导入文件，根据参数区分导入业务
         * @summary 导入文件
         * @param {ImportsRequest} importsRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async create(importsRequest: ImportsRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOImportsSubmitVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.create(importsRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['导入接口Api.create']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 分页导入任务列表
         * @summary 分页导入任务列表
         * @param {ImportsTaskReq} importsTaskReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listByPage(importsTaskReq: ImportsTaskReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOImportsTaskVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listByPage(importsTaskReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['导入接口Api.listByPage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 导入接口Api - factory interface
 * @export
 */
export const 导入接口ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 导入接口ApiFp(configuration)
    return {
        /**
         * 导入文件，根据参数区分导入业务
         * @summary 导入文件
         * @param {导入接口ApiCreateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create(requestParameters: 导入接口ApiCreateRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOImportsSubmitVO> {
            return localVarFp.create(requestParameters.importsRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 分页导入任务列表
         * @summary 分页导入任务列表
         * @param {导入接口ApiListByPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listByPage(requestParameters: 导入接口ApiListByPageRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOImportsTaskVO> {
            return localVarFp.listByPage(requestParameters.importsTaskReq, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for create operation in 导入接口Api.
 * @export
 * @interface 导入接口ApiCreateRequest
 */
export interface 导入接口ApiCreateRequest {
    /**
     * 
     * @type {ImportsRequest}
     * @memberof 导入接口ApiCreate
     */
    readonly importsRequest: ImportsRequest
}

/**
 * Request parameters for listByPage operation in 导入接口Api.
 * @export
 * @interface 导入接口ApiListByPageRequest
 */
export interface 导入接口ApiListByPageRequest {
    /**
     * 
     * @type {ImportsTaskReq}
     * @memberof 导入接口ApiListByPage
     */
    readonly importsTaskReq: ImportsTaskReq
}

/**
 * 导入接口Api - object-oriented interface
 * @export
 * @class 导入接口Api
 * @extends {BaseAPI}
 */
export class 导入接口Api extends BaseAPI {
    /**
     * 导入文件，根据参数区分导入业务
     * @summary 导入文件
     * @param {导入接口ApiCreateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 导入接口Api
     */
    public create(requestParameters: 导入接口ApiCreateRequest, options?: RawAxiosRequestConfig) {
        return 导入接口ApiFp(this.configuration).create(requestParameters.importsRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 分页导入任务列表
     * @summary 分页导入任务列表
     * @param {导入接口ApiListByPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 导入接口Api
     */
    public listByPage(requestParameters: 导入接口ApiListByPageRequest, options?: RawAxiosRequestConfig) {
        return 导入接口ApiFp(this.configuration).listByPage(requestParameters.importsTaskReq, options).then((request) => request(this.axios, this.basePath));
    }
}

