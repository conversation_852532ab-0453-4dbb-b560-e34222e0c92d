/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOListCodeEnumVO } from '../models';
/**
 * 枚举相关Api - axios parameter creator
 * @export
 */
export const 枚举相关ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 导出任务状态
         * @summary 导出任务状态
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        exportTaskStatus: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/file/code/enums/export-task-status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 导出类型
         * @summary 导出类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        exportType: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/file/code/enums/export-type`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 导入任务状态
         * @summary 导入任务状态
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importsTaskStatus: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/file/code/enums/imports-task-status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 导入类型
         * @summary 导入类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importsType: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/file/code/enums/imports-type`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 枚举相关Api - functional programming interface
 * @export
 */
export const 枚举相关ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 枚举相关ApiAxiosParamCreator(configuration)
    return {
        /**
         * 导出任务状态
         * @summary 导出任务状态
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async exportTaskStatus(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListCodeEnumVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.exportTaskStatus(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['枚举相关Api.exportTaskStatus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 导出类型
         * @summary 导出类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async exportType(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListCodeEnumVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.exportType(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['枚举相关Api.exportType']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 导入任务状态
         * @summary 导入任务状态
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async importsTaskStatus(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListCodeEnumVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.importsTaskStatus(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['枚举相关Api.importsTaskStatus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 导入类型
         * @summary 导入类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async importsType(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListCodeEnumVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.importsType(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['枚举相关Api.importsType']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 枚举相关Api - factory interface
 * @export
 */
export const 枚举相关ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 枚举相关ApiFp(configuration)
    return {
        /**
         * 导出任务状态
         * @summary 导出任务状态
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        exportTaskStatus(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListCodeEnumVO> {
            return localVarFp.exportTaskStatus(options).then((request) => request(axios, basePath));
        },
        /**
         * 导出类型
         * @summary 导出类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        exportType(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListCodeEnumVO> {
            return localVarFp.exportType(options).then((request) => request(axios, basePath));
        },
        /**
         * 导入任务状态
         * @summary 导入任务状态
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importsTaskStatus(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListCodeEnumVO> {
            return localVarFp.importsTaskStatus(options).then((request) => request(axios, basePath));
        },
        /**
         * 导入类型
         * @summary 导入类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importsType(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListCodeEnumVO> {
            return localVarFp.importsType(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * 枚举相关Api - object-oriented interface
 * @export
 * @class 枚举相关Api
 * @extends {BaseAPI}
 */
export class 枚举相关Api extends BaseAPI {
    /**
     * 导出任务状态
     * @summary 导出任务状态
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 枚举相关Api
     */
    public exportTaskStatus(options?: RawAxiosRequestConfig) {
        return 枚举相关ApiFp(this.configuration).exportTaskStatus(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 导出类型
     * @summary 导出类型
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 枚举相关Api
     */
    public exportType(options?: RawAxiosRequestConfig) {
        return 枚举相关ApiFp(this.configuration).exportType(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 导入任务状态
     * @summary 导入任务状态
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 枚举相关Api
     */
    public importsTaskStatus(options?: RawAxiosRequestConfig) {
        return 枚举相关ApiFp(this.configuration).importsTaskStatus(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 导入类型
     * @summary 导入类型
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 枚举相关Api
     */
    public importsType(options?: RawAxiosRequestConfig) {
        return 枚举相关ApiFp(this.configuration).importsType(options).then((request) => request(this.axios, this.basePath));
    }
}

