/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 导入文件请求数据
 * @export
 * @interface ImportsRequest
 */
export interface ImportsRequest {
    /**
     * 文件Path
     * @type {string}
     * @memberof ImportsRequest
     */
    'filePath': string;
    /**
     * 文件名称
     * @type {string}
     * @memberof ImportsRequest
     */
    'fileName': string;
    /**
     * 导入文件类型,用来确定后续业务
     * @type {string}
     * @memberof ImportsRequest
     */
    'importsType': ImportsRequestImportsTypeEnum;
    /**
     * 导入文件业务参数
     * @type {string}
     * @memberof ImportsRequest
     */
    'paramJson'?: string;
}

export const ImportsRequestImportsTypeEnum = {
    CATEGORY: 'IMPORTS_CATEGORY',
    PRODUCT: 'IMPORTS_PRODUCT',
    SHOPLINE_META_FIELD: 'SHOPLINE_META_FIELD'
} as const;

export type ImportsRequestImportsTypeEnum = typeof ImportsRequestImportsTypeEnum[keyof typeof ImportsRequestImportsTypeEnum];


