/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface UnzipRequest
 */
export interface UnzipRequest {
    /**
     * 文件路径
     * @type {string}
     * @memberof UnzipRequest
     */
    'filePath'?: string;
    /**
     * 上传文件类型
     * @type {string}
     * @memberof UnzipRequest
     */
    'uploadFileType'?: UnzipRequestUploadFileTypeEnum;
    /**
     * 压缩业务类型
     * @type {string}
     * @memberof UnzipRequest
     */
    'zipBizType': UnzipRequestZipBizTypeEnum;
    /**
     * 操作人账号
     * @type {number}
     * @memberof UnzipRequest
     */
    'createUserCode'?: number;
}

export const UnzipRequestUploadFileTypeEnum = {
    CATEGORY_ICON: 'category/icon',
    CATEGORY_IMPORT: 'category/import',
    PRODUCT_DESC: 'product/desc',
    PRODUCT_IMAGES_MAIN: 'product/images/main',
    PRODUCT_IMAGES_SKU: 'product/images/sku',
    PRODUCT_IMAGES_DETAIL: 'product/images/detail',
    PRODUCT_FILES: 'product/files',
    PRODUCT_MATERIAL: 'product/material',
    PRODUCT_FILES_INIT: 'product/files/init',
    PRODUCT_EXPORT_META_FIELD: 'product/export/meta_field',
    PRODUCT_EXPORT_COMMODITIES: 'product/export/commodities',
    IMPORTS_ERROR: 'imports/error',
    OTHERS: 'others'
} as const;

export type UnzipRequestUploadFileTypeEnum = typeof UnzipRequestUploadFileTypeEnum[keyof typeof UnzipRequestUploadFileTypeEnum];
export const UnzipRequestZipBizTypeEnum = {
    UNZIP_PRODUCT_FILE: 'unzip_product_file',
    UNZIP_INIT_PRODUCT_FILE: 'unzip_init_product_file',
    ZIP: 'zip'
} as const;

export type UnzipRequestZipBizTypeEnum = typeof UnzipRequestZipBizTypeEnum[keyof typeof UnzipRequestZipBizTypeEnum];


