/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 当前页结果集
 * @export
 * @interface ImportsTaskVO
 */
export interface ImportsTaskVO {
    /**
     * id
     * @type {number}
     * @memberof ImportsTaskVO
     */
    'id'?: number;
    /**
     * 任务编码
     * @type {string}
     * @memberof ImportsTaskVO
     */
    'taskCode'?: string;
    /**
     * 导入类型
     * @type {string}
     * @memberof ImportsTaskVO
     */
    'importsType'?: ImportsTaskVOImportsTypeEnum;
    /**
     * 导入进度状态
     * @type {number}
     * @memberof ImportsTaskVO
     */
    'status'?: ImportsTaskVOStatusEnum;
    /**
     * 文件路径
     * @type {string}
     * @memberof ImportsTaskVO
     */
    'filePath'?: string;
    /**
     * 文件名
     * @type {string}
     * @memberof ImportsTaskVO
     */
    'fileName'?: string;
    /**
     * 临时文件路径
     * @type {string}
     * @memberof ImportsTaskVO
     */
    'tempFilePath'?: string;
    /**
     * 失败原因文件
     * @type {string}
     * @memberof ImportsTaskVO
     */
    'failedFilePath'?: string;
    /**
     * 失败原因简述
     * @type {string}
     * @memberof ImportsTaskVO
     */
    'failedReason'?: string;
    /**
     * 操作人
     * @type {number}
     * @memberof ImportsTaskVO
     */
    'userId'?: number;
    /**
     * 创建时间
     * @type {string}
     * @memberof ImportsTaskVO
     */
    'createTime'?: string;
    /**
     * 任务完成时间
     * @type {string}
     * @memberof ImportsTaskVO
     */
    'finishTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof ImportsTaskVO
     */
    'updateTime'?: string;
}

export const ImportsTaskVOImportsTypeEnum = {
    CATEGORY: 'IMPORTS_CATEGORY',
    PRODUCT: 'IMPORTS_PRODUCT',
    SHOPLINE_META_FIELD: 'SHOPLINE_META_FIELD'
} as const;

export type ImportsTaskVOImportsTypeEnum = typeof ImportsTaskVOImportsTypeEnum[keyof typeof ImportsTaskVOImportsTypeEnum];
export const ImportsTaskVOStatusEnum = {
    DRAFT: 0,
    PENDING: 1,
    SUCCESS: 2,
    FAILED: 3,
    SUCCESS_PART: 4,
    DELETED: 9
} as const;

export type ImportsTaskVOStatusEnum = typeof ImportsTaskVOStatusEnum[keyof typeof ImportsTaskVOStatusEnum];


