/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 分页查询导入任务请求对象
 * @export
 * @interface ImportsTaskReq
 */
export interface ImportsTaskReq {
    /**
     * 当前页码
     * @type {number}
     * @memberof ImportsTaskReq
     */
    'pageNum'?: number;
    /**
     * 分页大小
     * @type {number}
     * @memberof ImportsTaskReq
     */
    'pageSize'?: number;
    /**
     * 排序规则
     * @type {string}
     * @memberof ImportsTaskReq
     */
    'orderBy'?: string;
    /**
     * 生成开始时间
     * @type {number}
     * @memberof ImportsTaskReq
     */
    'id'?: number;
    /**
     * 记录编码
     * @type {string}
     * @memberof ImportsTaskReq
     */
    'taskCode'?: string;
    /**
     * 文件名
     * @type {string}
     * @memberof ImportsTaskReq
     */
    'fileName'?: string;
    /**
     * 导入类型
     * @type {string}
     * @memberof ImportsTaskReq
     */
    'importsType'?: ImportsTaskReqImportsTypeEnum;
    /**
     * 导入进度状态
     * @type {number}
     * @memberof ImportsTaskReq
     */
    'status'?: ImportsTaskReqStatusEnum;
    /**
     * 完成时间范围-开始
     * @type {string}
     * @memberof ImportsTaskReq
     */
    'finishStartTime'?: string;
    /**
     * 完成时间范围-结束
     * @type {string}
     * @memberof ImportsTaskReq
     */
    'finishEndTime'?: string;
    /**
     * 创建时间范围-开始
     * @type {string}
     * @memberof ImportsTaskReq
     */
    'createStartTime'?: string;
    /**
     * 创建时间范围-结束
     * @type {string}
     * @memberof ImportsTaskReq
     */
    'createEndTime'?: string;
    /**
     * 操作人账号
     * @type {number}
     * @memberof ImportsTaskReq
     */
    'createUserCode'?: number;
}

export const ImportsTaskReqImportsTypeEnum = {
    CATEGORY: 'IMPORTS_CATEGORY',
    PRODUCT: 'IMPORTS_PRODUCT',
    SHOPLINE_META_FIELD: 'SHOPLINE_META_FIELD'
} as const;

export type ImportsTaskReqImportsTypeEnum = typeof ImportsTaskReqImportsTypeEnum[keyof typeof ImportsTaskReqImportsTypeEnum];
export const ImportsTaskReqStatusEnum = {
    DRAFT: 0,
    PENDING: 1,
    SUCCESS: 2,
    FAILED: 3,
    SUCCESS_PART: 4,
    DELETED: 9
} as const;

export type ImportsTaskReqStatusEnum = typeof ImportsTaskReqStatusEnum[keyof typeof ImportsTaskReqStatusEnum];


