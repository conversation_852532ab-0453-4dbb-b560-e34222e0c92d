/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 导入回调请求
 * @export
 * @interface TaskCallbackRequest
 */
export interface TaskCallbackRequest {
    /**
     * 任务编码
     * @type {string}
     * @memberof TaskCallbackRequest
     */
    'taskCode': string;
    /**
     * 是否结束
     * @type {boolean}
     * @memberof TaskCallbackRequest
     */
    'isFinished': boolean;
    /**
     * 结束时，是否存在成功
     * @type {boolean}
     * @memberof TaskCallbackRequest
     */
    'isSuccess'?: boolean;
    /**
     * 全局错误信息
     * @type {string}
     * @memberof TaskCallbackRequest
     */
    'failedReason'?: string;
    /**
     * 错误明细信息的文件路径
     * @type {string}
     * @memberof TaskCallbackRequest
     */
    'failedFilePath'?: string;
    /**
     * 异常信息
     * @type {string}
     * @memberof TaskCallbackRequest
     */
    'failedException'?: string;
    /**
     * 进度，0到100
     * @type {number}
     * @memberof TaskCallbackRequest
     */
    'progress'?: number;
}

