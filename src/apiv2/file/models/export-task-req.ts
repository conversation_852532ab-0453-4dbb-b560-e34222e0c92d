/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 下载中心请求对象
 * @export
 * @interface ExportTaskReq
 */
export interface ExportTaskReq {
    /**
     * 导出业务类型
     * @type {number}
     * @memberof ExportTaskReq
     */
    'exportBizType'?: ExportTaskReqExportBizTypeEnum;
    /**
     * 导出参数
     * @type {string}
     * @memberof ExportTaskReq
     */
    'exportParams'?: string;
}

export const ExportTaskReqExportBizTypeEnum = {
    PRODUCT_EXPORT: 0,
    COMMODITY_SHOP_LINE_EXPORT: 1,
    PRODUCT_META_FIELD_EXPORT: 2
} as const;

export type ExportTaskReqExportBizTypeEnum = typeof ExportTaskReqExportBizTypeEnum[keyof typeof ExportTaskReqExportBizTypeEnum];


