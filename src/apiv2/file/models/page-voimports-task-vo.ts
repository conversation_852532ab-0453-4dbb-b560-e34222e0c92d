/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ImportsTaskVO } from './imports-task-vo';

/**
 * API分页结果
 * @export
 * @interface PageVOImportsTaskVO
 */
export interface PageVOImportsTaskVO {
    /**
     * 当前页码
     * @type {number}
     * @memberof PageVOImportsTaskVO
     */
    'pageNum'?: number;
    /**
     * 每页大小
     * @type {number}
     * @memberof PageVOImportsTaskVO
     */
    'pageSize'?: number;
    /**
     * 总记录数
     * @type {number}
     * @memberof PageVOImportsTaskVO
     */
    'total'?: number;
    /**
     * 总页数
     * @type {number}
     * @memberof PageVOImportsTaskVO
     */
    'pages'?: number;
    /**
     * 当前页结果集
     * @type {Array<ImportsTaskVO>}
     * @memberof PageVOImportsTaskVO
     */
    'records'?: Array<ImportsTaskVO>;
}

